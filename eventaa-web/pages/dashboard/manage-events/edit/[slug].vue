<template>
    <CoreOverlay :loading="loading" text="Updating event..." />
    <div class="flex flex-col min-h-screen">
        <div class="min-h-screen relative">
            <div class="w-full bg-pattern backdrop-blur-sm">
                <div class="px-4 py-4 flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <button @click="$router.back()" class="text-gray-600 dark:text-zinc-400 hover:text-gray-800 dark:hover:text-zinc-300">
                            <Icon icon="ion:arrow-back-sharp" class="w-6 h-6" />
                        </button>
                        <h1 class="text-2xl font-semibold dark:text-white">Edit Event</h1>
                    </div>
                </div>

                <div class="px-4 py-4">
                    <CoreStepper :steps="formattedSteps" :current-step="currentStep"
                        :progress-percentage="progressPercentage" />
                </div>
            </div>
            <div class="mx-5 shadow-sm bg-white dark:bg-zinc-900 dark:border-zinc-800">
                <FormKit v-if="currentStep == 1" id="createEventForm" @submit="onFormSubmit" type="form"
                    submit-label="Update" @submit-invalid="onFormInvalid" :actions="false" #default="{ }">
                    <div class="flex-grow">
                        <div class="bg-white dark:bg-zinc-900 px-6 py-4 border-b dark:border-zinc-800">
                            <h3 class="text-lg text-black font-semibold dark:text-zinc-400">Basic Information</h3>
                        </div>

                        <div class="w-full relative bg-white dark:bg-zinc-900 sm:grid sm:grid-cols-3 gap-6 p-6">
                            <div class="col-span-2 order-1">
                                <div class="flex flex-col space-y-3">
                                    <FormKit type="text" name="title" label="Title" v-model="title"
                                        placeholder="Enter the title of your event" validation="required" :classes="{
                                            input: 'pl-0',
                                            prefixIcon: 'w-0 h-0'
                                        }" />

                                    <div class="form-group">
                                        <label
                                            class="block text-base font-medium mb-2 dark:text-zinc-200">Description</label>
                                        <RichTextEditor theme="snow" class="editor dark:text-white" required
                                            v-model:content="description" contentType="html" :editor="ClassicEditor"
                                            v-model="description" :config="editorConfig"></RichTextEditor>
                                    </div>
                                    <!-- Start & End Date -->
                                    <FormKit type="group" name="dateGroup">
                                        <label class="block text-base font-medium dark:text-zinc-200">Start & End
                                            Date</label>
                                        <datepicker @cleared="isCleared" required position="left"
                                            placeholder="Select start & end date" :range="true"
                                            input-class-name="w-full px-4 py-3 border border-gray-200 dark:border-zinc-700 dark:bg-zinc-800 dark:text-white"
                                            format="dd/MM/yyyy HH:mm" v-model="dateRange" />
                                    </FormKit>

                                    <FormKit type="group" name="categoryGroup">
                                        <label
                                            class="block text-base font-medium mb-3 dark:text-zinc-200">Category</label>
                                        <div class="w-full border">
                                            <CoreImageDropdown :items="$categories" v-model="selectedCategory" />
                                        </div>
                                    </FormKit>

                                    <FormKit type="radio" name="locationType" label="Location type"
                                        v-model="selectedLocationType" :options="[
                                            { label: 'Online', value: 'Online' },
                                            { label: 'Venue', value: 'Venue' }
                                        ]" :classes="{
                                        label: 'block text-base font-medium mb-2 dark:text-zinc-200',
                                        options: 'flex space-x-4 mt-1',
                                        option: 'flex items-center cursor-pointer',
                                        input: 'form-radio h-4 w-4 text-red-600 focus:ring-red-500 dark:bg-zinc-800',
                                        optionLabel: 'ml-2 dark:text-zinc-300'
                                    }" />

                                    <FormKit v-if="selectedLocationType === 'Online'" type="url" name="meetingLink"
                                        label="Meeting link" v-model="meetingLink"
                                        placeholder="Enter or copy & paste meeting link" validation="url"
                                        prefix-icon="globe" />

                                    <FormKit v-else-if="selectedLocationType == 'Venue'" type="group"
                                        name="locationGroup">
                                        <label class="block text-base font-medium mb-2 dark:text-zinc-200">Location
                                            Picker</label>
                                        <CoreLocationPicker @update:location="onUpdateLocation" />
                                        <div v-if="location?.address || location?.street"
                                            class="mt-3 p-3 bg-gray-50 dark:bg-zinc-800 border border-gray-200 dark:border-zinc-700 text-gray-700 dark:text-zinc-300">
                                            <div v-if="location?.address">{{ location?.address }}</div>
                                            <div v-else-if="location?.street">{{ location?.street }}, {{ location?.city
                                                }}, {{ location?.country }}</div>
                                        </div>
                                    </FormKit>

                                    <FormKit type="radio" name="visibility" label="Visibility"
                                        v-model="selectedVisibility" :options="[
                                            { label: 'Public', value: 'Public' },
                                            { label: 'Private', value: 'Private' }
                                        ]" :classes="{
                                        label: 'block text-base font-medium mb-2 dark:text-zinc-200',
                                        options: 'flex space-x-4 mt-1',
                                        option: 'flex items-center cursor-pointer',
                                        input: 'form-radio h-4 w-4 text-red-600 focus:ring-red-500 dark:bg-zinc-800',
                                        optionLabel: 'ml-2 dark:text-zinc-300'
                                    }" />
                                </div>
                            </div>

                            <div class="col-span-1 order-2">
                                <div
                                    class="bg-white dark:bg-zinc-900 border border-gray-200 dark:border-zinc-800 shadow-sm">
                                    <div
                                        class="w-full bg-gray-50 dark:bg-zinc-800 flex items-center justify-between border-b dark:border-zinc-700 px-4 py-3">
                                        <h3 class="text-lg font-medium dark:text-white">Uploads</h3>
                                        <button
                                            class="text-gray-500 dark:text-zinc-400 hover:text-gray-700 dark:hover:text-zinc-300">
                                            <Icon icon="lucide:minimize-2" class="w-5 h-5" />
                                        </button>
                                    </div>
                                    <div class="p-4">
                                        <h4 class="text-base font-medium mb-3 dark:text-zinc-200">Cover Art</h4>
                                        <EventsImagePicker v-if="!loading" @files-selected="onCoverPicker"
                                            @file-removed="onFileRemoved"
                                            :file="`${runtimeConfig.public.baseUrl}storage/events/${coverArt}`" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="hidden">
                        <CoreSubmitButton ref="submitButtonRef" color="primary" :loading="loading" />
                    </div>
                </FormKit>
            </div>
            <div
                class="absolute bottom-0 left-0 right-0 bg-white dark:bg-black shadow border-t dark:border-zinc-800 p-4">
                <div class="flex justify-between items-center">
                    <button v-if="currentStep > 1" @click="currentStep--"
                        class="border dark:border-zinc-700 text-gray-700 dark:text-zinc-300 hover:bg-gray-50 dark:hover:bg-zinc-800 px-4 py-2 flex items-center transition-colors duration-200">
                        <Icon icon="fluent:arrow-previous-16-regular" class="w-5 h-5 mr-2" />
                        Previous
                    </button>
                    <div v-else></div>

                    <button @click="navigateNextStep"
                        class="bg-red-600 hover:bg-red-700 text-white flex items-center px-4 py-2 transition-colors duration-200">
                        Update Event
                        <Icon icon="fluent:arrow-next-16-regular" class="w-5 h-5 ml-2" />
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import type { Category, EventItem, Location } from '@/types';
import { ClassicEditor, Bold, Essentials, Heading, BlockQuote, Table, MediaEmbed, Font, FontColor, FontSize, Italic, Mention, Paragraph, Undo, Link, InputNumberView, List, } from 'ckeditor5';
import dayjs from 'dayjs';

const route = useRoute();

definePageMeta({
    layout: "dashboard",
});
useHead({
    title: "Edit Event - EventaHub Malawi",
});

const runtimeConfig = useRuntimeConfig();
const currentStep = ref<number>(1);
const submitButtonRef = ref()
const steps = ref([
    { name: 'Basic Information', completed: false },
    { name: 'Tickets', completed: false },
    { name: 'Sponsors', completed: false },
    { name: 'Publish', completed: false }
]);

const formattedSteps = computed(() => {
    return steps.value.map((step, index) => ({
        step: index + 1,
        label: step.name,
        sublabel: step.completed ? 'Completed' : ''
    }));
});

const progressPercentage = computed(() => {
    const completedSteps = steps.value.filter(step => step.completed).length;
    return Math.round((completedSteps / steps.value.length) * 100);
});

const eventDetails = ref<EventItem>();
const title = ref<string>("");
const description = ref<string>("");
const selectedCategory = ref<Category>({ id: 0, name: "select Category", icon: "other.png" });
const selectedLocationType = ref<string>("");
const selectedVisibility = ref<string>("");
const dateRange = ref<any>([]);
const { start, finish } = useLoadingIndicator();
const location = ref<Location | any>();
const coverArt = ref();
const meetingLink = ref<string>("");
const meetingIcon = ref<string>("globe");
const loading = ref<boolean>(false);
const event = ref<EventItem | null>(null);
const { $toast, $categories }: any = useNuxtApp();
const httpClient = useHttpClient();

const editorConfig = {
    plugins: [Bold, Essentials, Heading, Italic, BlockQuote, Table, Font, FontColor, FontSize, MediaEmbed, Mention, Paragraph, Undo, Link, InputNumberView, List],
    toolbar: ['heading',
        '|',
        'bold',
        'italic',
        'link',
        'bulletedList',
        'numberedList',
        'blockQuote',
        'insertTable',
        'mediaEmbed',
        'undo',
        'redo',
        'imageUpload',
        'fontSize',
        'fontColor',
        'highlight'],
};

const onFormInvalid = (e: any) => {
    console.log(e);
};

const stepActions = {
    1: () => submitButtonRef.value?.$el.click(),
    default: () => currentStep.value++
};

const navigateNextStep = (): void => {
    const action = stepActions[currentStep.value as keyof typeof stepActions] || stepActions.default;
    action();
};

const fetchEvent = async (): Promise<void> => {
    loading.value = true;
    try {
        const response: any = await httpClient.get(`${ENDPOINTS.EVENTS.SLUG}/${route.params.slug}`);
        event.value = response.event;
        populateForm(response.event);
    } catch (error: any) {
        $toast.error(error.message || 'Failed to fetch event');
    } finally {
        loading.value = false;
    }
};

const populateForm = (eventData: EventItem): void => {
    eventDetails.value = eventData;
    title.value = eventData.title;
    description.value = eventData.description;
    selectedCategory.value = $categories.find((cat: Category) => cat.id === eventData.category_id) || selectedCategory.value;
    selectedLocationType.value = eventData.location == "Remote" ? "Online" : "Venue";
    location.value = eventData.location;
    selectedVisibility.value = eventData.type_id == 1 ? "Public" : "Private";
    coverArt.value = eventData.cover_art;
    dateRange.value = [
        dayjs(eventData.start).toDate(),
        dayjs(eventData.end).toDate()
    ];

    if (eventData.location === "Online") {
        meetingLink.value = eventData.meeting_link?.link || "";
    }
};

const isCleared = (event: any): void => {
    console.log(event);
};

const onUpdateLocation = (e: any): void => {
    location.value = e;
}



const onFileRemoved = () => {
    $toast.warn("Image removed, please upload a new one");
}

const onCoverPicker = (e: File[]) => {
    coverArt.value = e[0];
}


const onFormSubmit = async (): Promise<void> => {
    loading.value = true;
    start();
    const formData = new FormData();

    formData.append("title", title.value);
    formData.append("description", description.value);
    formData.append("category", String(selectedCategory?.value?.id));

    if (selectedLocationType.value === "Venue") {
        const newLocationString = `${location?.value?.street || ''}, ${location?.value?.city || ''}, ${location?.value?.country || ''}`.trim();

        if (newLocationString !== eventDetails.value?.location) {
            formData.append("location", newLocationString);
            formData.append("latitude", location?.value?.latlong?.lat?.toString() || eventDetails.value?.latitude?.toString() || '');
            formData.append("longitude", location?.value?.latlong?.lng?.toString() || eventDetails.value?.longitude?.toString() || '');
            formData.append("district", location?.value?.district || "");
        }
    } else {
        if (meetingLink.value !== eventDetails.value?.meeting_link?.link) {
            formData.append("meeting_link", meetingLink.value);
        }
    }

    const newVisibility = selectedVisibility.value === "Public" ? "1" : "0";
    formData.append("visibility", newVisibility);
    formData.append("type", newVisibility);

    const startDate = dayjs(dateRange.value[0]).format("YYYY-MM-DD HH:mm:ss");
    const endDate = dayjs(dateRange.value[1]).format("YYYY-MM-DD HH:mm:ss");
    formData.append("start_date", startDate);
    formData.append("end_date", endDate);
    formData.append("locationType", selectedLocationType.value);
    if (coverArt.value === eventDetails.value?.cover_art) {
        const url = `${runtimeConfig.public.baseUrl}storage/events/${coverArt.value}`;
        fetch(url)
            .then((response) => response.blob())
            .then((blob) => {
                const file = new File([blob], "cover_art.jpg", { type: blob.type });
                formData.append("cover_art", file);
            })
            .catch((error) => {
                console.error("Error fetching the image:", error);
            });
    } else {
        formData.append("cover_art", coverArt.value);
    }


    try {
        const response: any = await httpClient.post(`${ENDPOINTS.EVENTS.UPDATE}/${Number(eventDetails.value?.id)}`, formData);
        $toast.success(response.message || 'Event updated successfully');
        navigateTo("/dashboard/manage-events");
    } catch (error: any) {
        if (error) {
            const errors = error.errors;
            Object.keys(errors).forEach((key: string) => {
                if (Array.isArray(errors[key])) {
                    errors[key].forEach((message: string) => {
                        $toast.error(message);
                    });
                } else if (typeof errors[key] === 'string') {
                    $toast.error(errors[key]);
                }
            });
        } else if (error.message) {
            $toast.error(error.message);
        } else {
            $toast.error('Something went wrong, please try again later');
        }
        finish();
    } finally {
        loading.value = false;
        finish();
    }
};

watch(meetingLink, (newMeetingLink) => {
    const url = newMeetingLink.toLowerCase();
    const iconMap = {
        'zoom.us': 'zoom',
        'teams.microsoft.com': 'teams',
        'meet.google.com': 'meet',
    };
    let icon = "globe";
    for (const [keyword, mappedIcon] of Object.entries(iconMap)) {
        if (url.includes(keyword)) {
            icon = mappedIcon;
        }
    }
    meetingIcon.value = icon;
});

onMounted(() => {
    fetchEvent();
});

</script>

<style lang="css" scoped>
.bg-pattern {
    background-image: radial-gradient(circle at 1px 1px, rgb(226 232 240 / 0.5) 1px, transparent 0);
    background-size: 24px 24px;
}
</style>
