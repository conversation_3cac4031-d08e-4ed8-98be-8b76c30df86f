<template>
    <div class="w-full">
        <Listbox v-model="selectedItem" as="div">
            <div class="relative mt-1">
                <ListboxButton
                    class="relative w-full cursor-default bg-white py-2 pl-3 pr-10 text-left focus:outline-none focus-visible:border-indigo-500 focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-orange-300 sm:text-sm">
                    
                    <div class="w-full flex items-center space-x-2 mr-4">
                        <img :src="`${runtimeConfig.public.baseUrl}storage/categories/${modelValue.icon}`" alt="Image" class="h-6 w-6 mr-2" />
                    <span class="block text-gray-500">{{ modelValue.name }}</span>
                    </div>
                    <span class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                        <ChevronUpDownIcon class="h-5 w-5 text-gray-400" aria-hidden="true" />
                    </span>
                </ListboxButton>

                <transition leave-active-class="transition duration-100 ease-in" leave-from-class="opacity-100"
                    leave-to-class="opacity-0">
                    <ListboxOptions
                        class="absolute mt-1 max-h-60 w-full overflow-auto rounded-none bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm">
                        <ListboxOption v-for="item in items" :key="getKey(item)" :value="item"
                            v-slot="{ active, selected }" as="template">
                            <li :class="[
                                active ? 'bg-red-100 text-red-900' : 'text-gray-900',
                                'relative flex justify-between cursor-default select-none py-2 pl-4 pr-4',
                            ]">
                                <span :class="[
                                    selected ? 'font-medium' : 'font-normal',
                                    'flex items-center space-x-3 truncate',
                                ]">
                                <img :src="`${runtimeConfig.public.baseUrl}storage/categories/${item.icon}`" alt="Image" class="h-6 w-6 mr-2" />
                                    {{ getDisplayValue(item) }}
                                </span>
                                <span v-if="selected"
                                    class="absolute inset-y-0 right-0 flex items-center pl-3 text-red-600">
                                    <CheckIcon class="h-5 w-5" aria-hidden="true" />
                                </span>
                            </li>
                        </ListboxOption>
                    </ListboxOptions>
                </transition>
            </div>
        </Listbox>
    </div>
</template>

<script setup lang="ts">
import {
    Listbox,
    ListboxButton,
    ListboxOptions,
    ListboxOption,
} from '@headlessui/vue'
import { CheckIcon, ChevronUpDownIcon } from '@heroicons/vue/20/solid'

interface Props<T> {
    modelValue: T
    items: T[]
    displayKey?: keyof T | ((item: T) => string)
    itemKey?: keyof T | ((item: T) => string | number)
}

const props = withDefaults(defineProps<Props<any>>(), {
    displayKey: 'name',
    itemKey: 'id'
});

const runtimeConfig = useRuntimeConfig()

const emit = defineEmits<{
    (e: 'update:modelValue', value: any): void
}>()

const selectedItem = computed({
    get() {
        return props.modelValue
    },
    set(value) {
        emit('update:modelValue', value)
    }
})

const getDisplayValue = (item: any): string => {
    if (!item) return ''
    if (typeof props.displayKey === 'function') {
        return props.displayKey(item)
    }
    return item[props.displayKey]
}

const getKey = (item: any): string | number => {
    if (typeof props.itemKey === 'function') {
        return props.itemKey(item)
    }
    return item[props.itemKey]
}
</script>