<template>
    <CoreDropdown :items="selectTypes" v-model="selectedType" @update:onClick="isItemClicked"/>
    <TransitionRoot appear :show="isOpen" as="template">
        <Dialog as="div" @close="closeModal" class="relative z-10">
            <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
                leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                <div class="fixed inset-0 bg-black/25" />
            </TransitionChild>

            <div class="fixed inset-0 overflow-y-auto">
                <div class="flex min-h-full items-center justify-center p-4 text-center">
                    <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
                        enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
                        leave-to="opacity-0 scale-95">
                        <DialogPanel
                            class="w-full max-w-2xl transform overflow-hidden bg-white text-left align-middle shadow-xl transition-all">
                            <DialogTitle as="h3" class="w-full flex items-center justify-between px-4 py-2 border-b">
                                <div class="flex items-center">
                                    <Icon icon="fa6-solid:map-location-dot" class="w-7 h-7 mr-3" />
                                    <h3 class=" text-xl font-semibold leading-6 text-gray-900">{{ selectedType.name }}
                                    </h3>
                                </div>
                                <button>
                                    <Icon icon="fa6-solid:xmark" class="w-5 h-5" @click="closeModal" />
                                </button>
                            </DialogTitle>
                            <div class="px-5 py-5">
                                <div v-if="selectedType.name == 'Manually enter location'"
                                    class="w-full flex flex-col space-y-2">
                                    <FormKit type="text" name="location" label="Street"
                                        placeholder="For example Near Ndirande Parish" v-model="manualDetails.street" />
                                    <FormKit type="text" name="location" label="City"
                                        placeholder="Enter city or district i.e, Lilongwe"
                                        v-model="manualDetails.city" />
                                    <div>
                                        <label class="text-lg font-semibold mb-2">Country</label>
                                        <country-select autocomplete
                                            className="w-full border bg-white px-4 py-2 focus:outline-none focus:ring-0"
                                            v-model="manualDetails.country" :country="country" topCountry="MW" />
                                    </div>
                                    <div class="w-full justify-end flex mt-2">
                                        <button @click="onSave" class="bg-red-600 px-4 py-1.5 text-white">
                                            Save
                                        </button>
                                    </div>
                                </div>
                                <CoreMapPicker v-else :api-key="runtimeConfig.public.googleMapsApiKey"
                                    @update:location="onLocationChange" @update:onSave="onSave" @update:address="onAddressChange" />
                            </div>
                        </DialogPanel>
                    </TransitionChild>
                </div>
            </div>
        </Dialog>
    </TransitionRoot>
</template>

<script setup lang="ts">
import {
    TransitionRoot,
    TransitionChild,
    Dialog,
    DialogPanel,
    DialogTitle,
} from '@headlessui/vue';

const emits = defineEmits(['update:location'])
const isOpen = ref<boolean>(false);
const runtimeConfig = useRuntimeConfig();
const country = ref<string>("Malawi")
const selectTypes = ref<{ name: string, id: number }[]>([
    { id: 1, name: "Pick location map" },
    { id: 2, name: "Manually enter location" }
]);
const selectedType = ref({ name: "-- click to select location form --" });
const manualDetails = ref<Record<string, string>>({
    street: "",
    city: "",
    country: ""
});
const latLong = ref<Object>();
const formattedAddress = ref<string>("");

const closeModal = (): void => {
    isOpen.value = false;
}
const openModal = (): void => {
    isOpen.value = true;
}

const onLocationChange = (location: Object): void => {
    latLong.value = location;
}

const onAddressChange = (address: string): void => {
    formattedAddress.value = address;
}

const onSave = (): void => {
    const options: Record<string, Object> = {
        "Pick location map" : { latlong: latLong.value, address: formattedAddress.value },
        "Manually enter location": manualDetails.value
    }
    emits('update:location', options[selectedType.value.name]);
    closeModal()
}

const isItemClicked = (e: any) => {
    if(e){
        selectedType.value = e;
        openModal();
    }
}

watch(selectedType, (n, o) => {
    openModal();
}, { deep: true });
;
</script>