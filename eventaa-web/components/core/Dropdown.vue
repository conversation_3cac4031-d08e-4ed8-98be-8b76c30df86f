<template>
    <div class="w-full">
        <Listbox v-model="selectedItem" as="div">
            <ListboxLabel class="text-base font-semibold text-black">
                {{ label }}
            </ListboxLabel>
            <div class="relative flex items-center mt-2">
                <div v-if="icon" class="flex items-center p-2 bg-red-600">
                    <Icon :icon="icon" class="w-5 h-5"/>
                </div>
                <ListboxButton
                    class="relative w-full cursor-default bg-white py-2 pl-3 pr-10 text-left border focus:outline-none focus-visible:border-indigo-500 focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-orange-300 sm:text-sm">
                    <span class="block truncate" :class="Number(modelValue.id) == 0 ?? 'text-zinc-400'">{{ getDisplayValue(modelValue) }} </span>
                    <span class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                        <ChevronUpDownIcon class="h-5 w-5 text-gray-400" aria-hidden="true" />
                    </span>
                </ListboxButton>

                <transition leave-active-class="transition duration-100 ease-in" leave-from-class="opacity-100"
                    leave-to-class="opacity-0">
                    <ListboxOptions
                        class="absolute z-50 mt-1 max-h-60 w-full overflow-auto rounded-none bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm">
                        <ListboxOption @click="emitClick(item)" v-for="item in items" :key="getKey(item)" :value="item"
                            v-slot="{ active, selected }" as="template">
                            <li :class="[
                                active ? 'bg-red-100 text-red-900' : 'text-gray-900',
                                'relative cursor-default select-none py-2 pl-10 pr-4',
                                item.id == 0 ? 'text-zinc-400' : 'text-gray-900',
                            ]">
                                <span :class="[
                                    selected ? 'font-medium' : 'font-normal',
                                    'block truncate',
                                ]">
                                    {{ getDisplayValue(item) }}
                                </span>
                                <span v-if="selected"
                                    class="absolute inset-y-0 left-0 flex items-center pl-3 text-red-600">
                                    <CheckIcon class="h-5 w-5" aria-hidden="true" />
                                </span>
                            </li>
                        </ListboxOption>
                    </ListboxOptions>
                </transition>
            </div>
        </Listbox>
    </div>
</template>

<script setup lang="ts">
import {
    Listbox,
    ListboxButton,
    ListboxOptions,
    ListboxOption,
    ListboxLabel,
} from '@headlessui/vue'
import { CheckIcon, ChevronUpDownIcon } from '@heroicons/vue/20/solid'

interface Props<T> {
    modelValue: T
    items: T[]
    displayKey?: keyof T | ((item: T) => string)
    itemKey?: keyof T | ((item: T) => string | number)
    label?: string;
    icon?: string;
}

const props = withDefaults(defineProps<Props<any>>(), {
    displayKey: 'name',
    itemKey: 'id'
})

const emit = defineEmits<{
    (e: 'update:modelValue', value: any): void,
    (e: 'update:onClick', value: any): void,
}>()

const selectedItem = computed({
    get() {
        return props.modelValue
    },
    set(value) {
        emit('update:modelValue', value)
    }
})

const getDisplayValue = (item: any): string => {
    if (!item) return ''
    if (typeof props.displayKey === 'function') {
        return props.displayKey(item)
    }
    return item[props.displayKey]
}

const emitClick = (item: any) => {
    selectedItem.value = item
    emit("update:onClick", item)
}

const getKey = (item: any): string | number => {
    if (typeof props.itemKey === 'function') {
        return props.itemKey(item)
    }
    return item[props.itemKey]
}
</script>